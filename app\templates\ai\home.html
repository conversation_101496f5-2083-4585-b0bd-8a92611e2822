{% extends "base.html" %}

{% block title %}AI Text Generation - Flask Demo App{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <div class="text-center mb-5">
            <h1 class="display-4">🤖 AI Text Generation</h1>
            <p class="lead">Powered by BLOOMZ-560M Language Model</p>
        </div>

        <!-- Model Status Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="fas fa-microchip"></i> Model Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Model:</strong> {{ model_info.model_name }}</p>
                        <p><strong>Status:</strong> 
                            {% if model_info.is_loaded %}
                                <span class="badge bg-success">Loaded</span>
                            {% elif model_info.has_token %}
                                <span class="badge bg-warning">Ready to Load</span>
                            {% else %}
                                <span class="badge bg-danger">Not Available</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>CUDA Available:</strong> 
                            {% if model_info.cuda_available %}
                                <span class="badge bg-success">Yes ({{ model_info.device_count }} devices)</span>
                            {% else %}
                                <span class="badge bg-secondary">No (CPU only)</span>
                            {% endif %}
                        </p>
                        <p><strong>Cache Directory:</strong> {{ model_info.cache_dir }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Cards -->
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-edit fa-3x text-primary mb-3"></i>
                        <h5 class="card-title">Text Generation</h5>
                        <p class="card-text">Generate creative text using the BLOOMZ-560M model with customizable parameters.</p>
                        {% if model_info.has_token %}
                            <a href="{{ url_for('main.ai_generate') }}" class="btn btn-primary">Start Generating</a>
                        {% else %}
                            <button class="btn btn-secondary" disabled>Configure HF Token First</button>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-rocket fa-3x text-success mb-3"></i>
                        <h5 class="card-title">Streamlit Interface</h5>
                        <p class="card-text">Access the advanced Streamlit interface for more interactive AI features.</p>
                        <a href="{{ url_for('main.streamlit_redirect') }}" class="btn btn-success" target="_blank">
                            Open Streamlit <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-star"></i> Features</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> BLOOMZ-560M Language Model</li>
                            <li><i class="fas fa-check text-success"></i> Customizable Generation Parameters</li>
                            <li><i class="fas fa-check text-success"></i> Real-time Text Generation</li>
                            <li><i class="fas fa-check text-success"></i> GPU Acceleration Support</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success"></i> RESTful API Endpoints</li>
                            <li><i class="fas fa-check text-success"></i> Streamlit Integration</li>
                            <li><i class="fas fa-check text-success"></i> Model Caching</li>
                            <li><i class="fas fa-check text-success"></i> Error Handling & Logging</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Setup Instructions (if not configured) -->
        {% if not model_info.has_token %}
        <div class="alert alert-warning mt-4">
            <h5><i class="fas fa-exclamation-triangle"></i> Setup Required</h5>
            <p>To use AI features, you need to configure your Hugging Face token:</p>
            <ol>
                <li>Get a token from <a href="https://huggingface.co/settings/tokens" target="_blank">Hugging Face</a></li>
                <li>Add <code>HF_TOKEN=your_token_here</code> to your <code>.env</code> file</li>
                <li>Restart the application</li>
            </ol>
        </div>
        {% endif %}

        <div class="text-center mt-4">
            <a href="{{ url_for('main.home') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Check AI service status periodically
function checkAIStatus() {
    fetch('/ai/status')
        .then(response => response.json())
        .then(data => {
            console.log('AI Status:', data);
        })
        .catch(error => {
            console.error('Error checking AI status:', error);
        });
}

// Check status every 30 seconds
setInterval(checkAIStatus, 30000);
</script>
{% endblock %}
