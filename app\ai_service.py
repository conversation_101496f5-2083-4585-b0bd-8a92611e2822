"""
AI Service Module for BLOOMZ-560M Text Generation
Handles model loading, caching, and text generation
"""

import os
import logging
from typing import Optional, Dict, Any
from functools import lru_cache
import torch
from transformers import AutoModelForCausalLM, AutoTokenizer
from flask import current_app
from dotenv import load_dotenv

load_dotenv()

logger = logging.getLogger(__name__)

class AIService:
    """Service class for handling AI text generation with BLOOMZ-560M model"""
    
    def __init__(self):
        self.model = None
        self.tokenizer = None
        self.model_name = os.environ.get('recommendation dataset', 'bigscience/bloomz-560m')
        self.hf_token = os.environ.get('*************************************')
        self.cache_dir = os.environ.get('AI_CACHE_DIR', './model_cache')
        self.is_loaded = False
        
    def load_model(self) -> bool:
        """
        Load the BLOOMZ-560M model and tokenizer
        Returns True if successful, False otherwise
        """
        try:
            if self.is_loaded:
                return True
                
            logger.info(f"Loading AI model: {self.model_name}")
            
            # Create cache directory if it doesn't exist
            os.makedirs(self.cache_dir, exist_ok=True)
            
            # Load tokenizer
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_name,
                use_auth_token=self.hf_token,
                cache_dir=self.cache_dir
            )
            
            # Load model
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                use_auth_token=self.hf_token,
                cache_dir=self.cache_dir,
                torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
                device_map="auto" if torch.cuda.is_available() else None
            )
            
            self.is_loaded = True
            logger.info("AI model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to load AI model: {str(e)}")
            self.is_loaded = False
            return False
    
    def generate_text(
        self, 
        prompt: str, 
        max_length: Optional[int] = None,
        temperature: Optional[float] = None,
        do_sample: bool = True,
        top_p: float = 0.9,
        top_k: int = 50
    ) -> Dict[str, Any]:
        """
        Generate text using the loaded model
        
        Args:
            prompt: Input text prompt
            max_length: Maximum length of generated text
            temperature: Sampling temperature (0.1-1.5)
            do_sample: Whether to use sampling
            top_p: Top-p sampling parameter
            top_k: Top-k sampling parameter
            
        Returns:
            Dictionary with generated text and metadata
        """
        if not self.is_loaded:
            if not self.load_model():
                return {
                    'success': False,
                    'error': 'AI model not available',
                    'generated_text': '',
                    'prompt': prompt
                }
        
        try:
            # Set default parameters
            max_length = max_length or int(os.environ.get('AI_MAX_LENGTH', 200))
            temperature = temperature or float(os.environ.get('AI_TEMPERATURE', 0.7))
            
            # Validate parameters
            max_length = max(50, min(max_length, 500))  # Clamp between 50-500
            temperature = max(0.1, min(temperature, 1.5))  # Clamp between 0.1-1.5
            
            # Encode input
            inputs = self.tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)
            
            # Move to GPU if available
            if torch.cuda.is_available():
                inputs = {k: v.cuda() for k, v in inputs.items()}
            
            # Generate text
            with torch.no_grad():
                outputs = self.model.generate(
                    inputs["input_ids"],
                    max_length=max_length,
                    temperature=temperature,
                    do_sample=do_sample,
                    top_p=top_p,
                    top_k=top_k,
                    pad_token_id=self.tokenizer.eos_token_id,
                    attention_mask=inputs.get("attention_mask")
                )
            
            # Decode generated text
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract only the new generated part (remove original prompt)
            if generated_text.startswith(prompt):
                new_text = generated_text[len(prompt):].strip()
            else:
                new_text = generated_text.strip()
            
            return {
                'success': True,
                'generated_text': generated_text,
                'new_text': new_text,
                'prompt': prompt,
                'parameters': {
                    'max_length': max_length,
                    'temperature': temperature,
                    'top_p': top_p,
                    'top_k': top_k
                }
            }
            
        except Exception as e:
            logger.error(f"Text generation failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'generated_text': '',
                'prompt': prompt
            }
    
    def is_available(self) -> bool:
        """Check if the AI service is available"""
        return self.is_loaded or self.hf_token is not None
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the loaded model"""
        return {
            'model_name': self.model_name,
            'is_loaded': self.is_loaded,
            'has_token': self.hf_token is not None,
            'cache_dir': self.cache_dir,
            'cuda_available': torch.cuda.is_available(),
            'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0
        }

# Global AI service instance
ai_service = AIService()

@lru_cache(maxsize=1)
def get_ai_service() -> AIService:
    """Get the global AI service instance"""
    return ai_service

def init_ai_service(app):
    """Initialize AI service with Flask app context"""
    with app.app_context():
        try:
            if ai_service.hf_token:
                logger.info("Initializing AI service...")
                ai_service.load_model()
            else:
                logger.warning("HF_TOKEN not provided, AI features will be disabled")
        except Exception as e:
            logger.error(f"Failed to initialize AI service: {str(e)}")
