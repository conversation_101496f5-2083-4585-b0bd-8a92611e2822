#!/usr/bin/env python3
"""
Demo script to test AI functionality with your HF_TOKEN
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def demo_ai_service():
    """Demonstrate AI service functionality"""
    print("🤖 Flask AI Demo - Testing with your HF_TOKEN")
    print("=" * 60)
    
    try:
        # Import AI service
        from app.ai_service import get_ai_service
        
        # Get AI service instance
        ai_service = get_ai_service()
        
        # Check configuration
        print("📋 Configuration:")
        model_info = ai_service.get_model_info()
        print(f"   Model: {model_info['model_name']}")
        print(f"   HF Token: {'✅ Configured' if model_info['has_token'] else '❌ Missing'}")
        print(f"   Cache Dir: {model_info['cache_dir']}")
        print(f"   CUDA: {'✅ Available' if model_info['cuda_available'] else '❌ CPU Only'}")
        
        if not model_info['has_token']:
            print("\n❌ HF_TOKEN is not configured. Please check your .env file.")
            return False
        
        print(f"\n🔄 Service Status: {'✅ Available' if ai_service.is_available() else '❌ Not Available'}")
        
        # Test text generation (this will download the model on first run)
        print("\n🎯 Testing Text Generation...")
        print("   Prompt: 'Once upon a time'")
        print("   (Note: First run will download the model - this may take a few minutes)")
        
        # Simple test generation
        result = ai_service.generate_text(
            prompt="Once upon a time",
            max_length=100,
            temperature=0.7
        )
        
        if result['success']:
            print("\n✅ Text Generation Successful!")
            print(f"   Generated: {result['generated_text'][:100]}...")
            print(f"   Parameters: {result['parameters']}")
        else:
            print(f"\n❌ Text Generation Failed: {result['error']}")
            return False
        
        print("\n🎉 AI Service is working correctly with your HF_TOKEN!")
        return True
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("   Make sure you're running from the project root directory")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def demo_flask_routes():
    """Test Flask routes without starting the server"""
    print("\n🌐 Testing Flask AI Routes...")
    print("=" * 30)
    
    try:
        from app import create_app
        
        # Create Flask app
        app = create_app('testing')
        
        with app.test_client() as client:
            # Test AI status endpoint
            response = client.get('/ai/status')
            print(f"   /ai/status: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
            
            if response.status_code == 200:
                data = response.get_json()
                print(f"   AI Available: {'✅' if data.get('available') else '❌'}")
            
            # Test AI home page
            response = client.get('/ai')
            print(f"   /ai: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
            
            # Test AI generate page
            response = client.get('/ai/generate')
            print(f"   /ai/generate: {response.status_code} {'✅' if response.status_code == 200 else '❌'}")
        
        print("✅ Flask AI routes are working!")
        return True
        
    except Exception as e:
        print(f"❌ Flask routes error: {e}")
        return False

def show_usage_instructions():
    """Show usage instructions"""
    print("\n📚 Usage Instructions")
    print("=" * 30)
    print("1. 🚀 Start Flask + Streamlit:")
    print("   ./deploy.sh both")
    print("   (or python run_both.py)")
    print()
    print("2. 🌐 Access your applications:")
    print("   Flask App: http://localhost:5000")
    print("   Flask AI:  http://localhost:5000/ai")
    print("   Streamlit: http://localhost:8501")
    print()
    print("3. 🎯 Try AI features:")
    print("   - Visit /ai for AI home page")
    print("   - Visit /ai/generate for text generation")
    print("   - Use the Streamlit interface for advanced controls")
    print()
    print("4. 🔧 API Usage:")
    print("   POST /ai/api/generate")
    print("   {\"prompt\": \"Your text\", \"max_length\": 200}")

if __name__ == "__main__":
    # Test AI service
    ai_ok = demo_ai_service()
    
    if ai_ok:
        # Test Flask routes
        flask_ok = demo_flask_routes()
        
        if flask_ok:
            print("\n🎉 All systems working! Your AI integration is ready!")
            show_usage_instructions()
        else:
            print("\n⚠️  AI service works, but Flask routes have issues.")
    else:
        print("\n❌ AI service is not working. Please check your configuration.")
        print("\n🔧 Troubleshooting:")
        print("   1. Verify HF_TOKEN in .env file")
        print("   2. Install dependencies: pip install -r requirements.txt")
        print("   3. Check internet connection for model download")
