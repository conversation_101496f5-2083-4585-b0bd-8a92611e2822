import os
from dotenv import load_dotenv
import streamlit as st
from transformers import AutoModelForCausalLM, AutoTokenizer

# Load environment variables
load_dotenv()

# Retrieve the Hugging Face token
hf_token = os.getenv("HF_TOKEN")

# Load the model and tokenizer using the token
@st.cache_resource
def load_model():
    model_name = "bigscience/bloomz-560m"
    tokenizer = AutoTokenizer.from_pretrained(model_name, use_auth_token=hf_token)
    model = AutoModelForCausalLM.from_pretrained(model_name, use_auth_token=hf_token)
    return tokenizer, model

# Initialize the model
tokenizer, model = load_model()

# Streamlit application
st.title("BloomZ-560M Text Generation")

st.markdown("### Input a prompt and get a completion using the BloomZ-560M model!")

# Input text
prompt = st.text_area("Enter your prompt:", value="Once upon a time", height=150)

# Generation settings
max_length = st.slider("Max Length:", 50, 500, 100)
temperature = st.slider("Temperature (Creativity):", 0.1, 1.5, 0.7)

if st.button("Generate Text"):
    with st.spinner("Generating..."):
        # Encode input prompt
        inputs = tokenizer(prompt, return_tensors="pt")
        # Generate output
        outputs = model.generate(
            inputs["input_ids"],
            max_length=max_length,
            temperature=temperature,
            do_sample=True,
        )
        # Decode and display result
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        st.subheader("Generated Text:")
        st.write(generated_text)
