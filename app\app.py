"""
Enhanced Streamlit App for BLOOMZ-560M Text Generation
Integrated with Flask Demo App
"""

import os
import sys
from dotenv import load_dotenv
import streamlit as st
from streamlit_option_menu import option_menu
import requests
import json

# Load environment variables
load_dotenv()

# Add the parent directory to the path to import from Flask app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from app.ai_service import get_ai_service
    FLASK_INTEGRATION = True
except ImportError:
    FLASK_INTEGRATION = False
    st.warning("Flask integration not available. Running in standalone mode.")

# Page configuration
st.set_page_config(
    page_title="AI Text Generation - Flask Demo",
    page_icon="🤖",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
.main-header {
    text-align: center;
    padding: 2rem 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    margin-bottom: 2rem;
}

.generation-box {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid #007bff;
    margin: 1rem 0;
}

.prompt-box {
    background: #e3f2fd;
    padding: 1rem;
    border-radius: 8px;
    border-left: 3px solid #2196f3;
    margin-bottom: 1rem;
}

.result-box {
    background: #f3e5f5;
    padding: 1rem;
    border-radius: 8px;
    border-left: 3px solid #9c27b0;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
}
</style>
""", unsafe_allow_html=True)

# Header
st.markdown("""
<div class="main-header">
    <h1>🤖 AI Text Generation</h1>
    <p>Powered by BLOOMZ-560M Language Model</p>
    <p><em>Enhanced Streamlit Interface for Flask Demo App</em></p>
</div>
""", unsafe_allow_html=True)

# Sidebar navigation
with st.sidebar:
    selected = option_menu(
        "Navigation",
        ["Text Generation", "Model Info", "API Testing", "Flask Integration"],
        icons=['robot', 'info-circle', 'code-slash', 'link'],
        menu_icon="cast",
        default_index=0,
    )

# Initialize AI service if Flask integration is available
if FLASK_INTEGRATION:
    ai_service = get_ai_service()
else:
    ai_service = None

# Text Generation Page
if selected == "Text Generation":
    st.header("🎯 Text Generation")

    col1, col2 = st.columns([1, 1])

    with col1:
        st.subheader("Input & Settings")

        # Input prompt
        prompt = st.text_area(
            "Enter your prompt:",
            value="Once upon a time in a magical forest,",
            height=150,
            help="Enter the text you want the AI to continue"
        )

        # Generation parameters
        st.subheader("Generation Parameters")

        col_a, col_b = st.columns(2)
        with col_a:
            max_length = st.slider("Max Length", 50, 500, 200, help="Maximum length of generated text")
        with col_b:
            temperature = st.slider("Temperature", 0.1, 1.5, 0.7, step=0.1, help="Creativity level")

        col_c, col_d = st.columns(2)
        with col_c:
            top_p = st.slider("Top-p", 0.1, 1.0, 0.9, step=0.1, help="Nucleus sampling")
        with col_d:
            top_k = st.slider("Top-k", 1, 100, 50, help="Top-k sampling")

        # Example prompts
        st.subheader("Example Prompts")
        examples = [
            "Once upon a time in a magical forest,",
            "The future of artificial intelligence will",
            "In a world where robots and humans coexist,",
            "The recipe for happiness includes",
            "Scientists have discovered that",
            "In the year 2050, technology will"
        ]

        selected_example = st.selectbox("Choose an example:", ["Custom"] + examples)
        if selected_example != "Custom":
            prompt = selected_example
            st.rerun()

    with col2:
        st.subheader("Generated Output")

        if st.button("🚀 Generate Text", type="primary", use_container_width=True):
            if not prompt.strip():
                st.error("Please enter a prompt!")
            else:
                with st.spinner("Generating text... This may take a moment."):
                    try:
                        if FLASK_INTEGRATION and ai_service:
                            # Use Flask AI service
                            result = ai_service.generate_text(
                                prompt=prompt,
                                max_length=max_length,
                                temperature=temperature,
                                top_p=top_p,
                                top_k=top_k
                            )

                            if result['success']:
                                st.success("Text generated successfully!")

                                # Display prompt
                                st.markdown("**Original Prompt:**")
                                st.markdown(f'<div class="prompt-box">{prompt}</div>', unsafe_allow_html=True)

                                # Display generated text
                                st.markdown("**Generated Text:**")
                                st.markdown(f'<div class="result-box">{result["generated_text"]}</div>', unsafe_allow_html=True)

                                # Display parameters
                                st.markdown("**Parameters Used:**")
                                params = result.get('parameters', {})
                                st.json(params)

                            else:
                                st.error(f"Generation failed: {result.get('error', 'Unknown error')}")
                        else:
                            # Fallback: try to use transformers directly
                            try:
                                from transformers import AutoModelForCausalLM, AutoTokenizer
                                import torch

                                @st.cache_resource
                                def load_model():
                                    model_name = "bigscience/bloomz-560m"
                                    hf_token = os.getenv("HF_TOKEN")
                                    tokenizer = AutoTokenizer.from_pretrained(model_name, use_auth_token=hf_token)
                                    model = AutoModelForCausalLM.from_pretrained(model_name, use_auth_token=hf_token)
                                    return tokenizer, model

                                tokenizer, model = load_model()

                                # Generate text
                                inputs = tokenizer(prompt, return_tensors="pt", truncation=True, max_length=512)

                                with torch.no_grad():
                                    outputs = model.generate(
                                        inputs["input_ids"],
                                        max_length=max_length,
                                        temperature=temperature,
                                        do_sample=True,
                                        top_p=top_p,
                                        top_k=top_k,
                                        pad_token_id=tokenizer.eos_token_id
                                    )

                                generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)

                                st.success("Text generated successfully!")
                                st.markdown("**Original Prompt:**")
                                st.markdown(f'<div class="prompt-box">{prompt}</div>', unsafe_allow_html=True)
                                st.markdown("**Generated Text:**")
                                st.markdown(f'<div class="result-box">{generated_text}</div>', unsafe_allow_html=True)

                            except Exception as e:
                                st.error(f"Direct model loading failed: {str(e)}")
                                st.info("Please ensure you have set up your HF_TOKEN in the environment variables.")

                    except Exception as e:
                        st.error(f"An error occurred: {str(e)}")

# Model Info Page
elif selected == "Model Info":
    st.header("📊 Model Information")

    if FLASK_INTEGRATION and ai_service:
        model_info = ai_service.get_model_info()

        col1, col2 = st.columns(2)

        with col1:
            st.subheader("Model Details")
            st.info(f"**Model Name:** {model_info['model_name']}")
            st.info(f"**Status:** {'✅ Loaded' if model_info['is_loaded'] else '⏳ Ready to Load' if model_info['has_token'] else '❌ Not Available'}")
            st.info(f"**Cache Directory:** {model_info['cache_dir']}")

        with col2:
            st.subheader("System Information")
            st.info(f"**CUDA Available:** {'✅ Yes' if model_info['cuda_available'] else '❌ No (CPU only)'}")
            if model_info['cuda_available']:
                st.info(f"**GPU Devices:** {model_info['device_count']}")
            st.info(f"**HF Token:** {'✅ Configured' if model_info['has_token'] else '❌ Not Set'}")
    else:
        st.warning("Flask integration not available. Model information cannot be retrieved.")

        # Show environment info
        st.subheader("Environment Information")
        hf_token = os.getenv("HF_TOKEN")
        st.info(f"**HF Token:** {'✅ Set' if hf_token else '❌ Not Set'}")

        try:
            import torch
            st.info(f"**PyTorch:** ✅ Available (v{torch.__version__})")
            st.info(f"**CUDA:** {'✅ Available' if torch.cuda.is_available() else '❌ Not Available'}")
        except ImportError:
            st.error("❌ PyTorch not available")

# API Testing Page
elif selected == "API Testing":
    st.header("🔧 API Testing")

    st.markdown("""
    Test the Flask API endpoints for AI text generation.
    Make sure your Flask app is running on the default port (5000).
    """)

    # API endpoint configuration
    col1, col2 = st.columns([2, 1])

    with col1:
        api_url = st.text_input("API Base URL", value="http://localhost:5000")

    with col2:
        if st.button("Test Connection"):
            try:
                response = requests.get(f"{api_url}/ai/status", timeout=5)
                if response.status_code == 200:
                    st.success("✅ Connection successful!")
                    st.json(response.json())
                else:
                    st.error(f"❌ Connection failed: {response.status_code}")
            except Exception as e:
                st.error(f"❌ Connection error: {str(e)}")

    # API testing form
    st.subheader("Test Text Generation API")

    api_prompt = st.text_area("API Test Prompt", value="Hello, world!")
    api_max_length = st.number_input("Max Length", min_value=50, max_value=500, value=100)
    api_temperature = st.number_input("Temperature", min_value=0.1, max_value=1.5, value=0.7, step=0.1)

    if st.button("🚀 Test API Generation"):
        try:
            payload = {
                "prompt": api_prompt,
                "max_length": api_max_length,
                "temperature": api_temperature
            }

            with st.spinner("Calling API..."):
                response = requests.post(
                    f"{api_url}/ai/api/generate",
                    json=payload,
                    headers={"Content-Type": "application/json"},
                    timeout=30
                )

            if response.status_code == 200:
                result = response.json()
                st.success("✅ API call successful!")

                if result.get('success'):
                    st.markdown("**Generated Text:**")
                    st.code(result['generated_text'])
                    st.markdown("**Parameters:**")
                    st.json(result.get('parameters', {}))
                else:
                    st.error(f"Generation failed: {result.get('error')}")
            else:
                st.error(f"API call failed: {response.status_code}")
                st.code(response.text)

        except Exception as e:
            st.error(f"API call error: {str(e)}")

# Flask Integration Page
elif selected == "Flask Integration":
    st.header("🔗 Flask Integration")

    if FLASK_INTEGRATION:
        st.success("✅ Flask integration is active!")

        st.markdown("""
        ### Available Features:
        - **Shared AI Service**: Using the same AI service as the Flask app
        - **Model Caching**: Shared model cache between Flask and Streamlit
        - **Configuration**: Using the same environment variables
        """)

        # Quick links
        st.subheader("Quick Links")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("🏠 Flask Home", use_container_width=True):
                st.markdown("[Open Flask App](http://localhost:5000)", unsafe_allow_html=True)

        with col2:
            if st.button("🤖 Flask AI", use_container_width=True):
                st.markdown("[Open Flask AI](http://localhost:5000/ai)", unsafe_allow_html=True)

        with col3:
            if st.button("📝 Flask Posts", use_container_width=True):
                st.markdown("[Open Flask Posts](http://localhost:5000/posts)", unsafe_allow_html=True)
    else:
        st.warning("⚠️ Flask integration is not available.")

        st.markdown("""
        ### To enable Flask integration:
        1. Make sure you're running this Streamlit app from the Flask project directory
        2. Ensure the Flask app modules are importable
        3. Check that all dependencies are installed

        ### Running in standalone mode:
        - Direct model loading using transformers
        - Independent configuration
        - No shared resources with Flask
        """)

# Footer
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #666;">
    <p>🤖 Enhanced Streamlit Interface for Flask Demo App</p>
    <p>Powered by BLOOMZ-560M Language Model</p>
</div>
""", unsafe_allow_html=True)
