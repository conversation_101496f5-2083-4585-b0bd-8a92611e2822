{% extends "base.html" %}

{% block title %}{{ post.title }} - Flask Demo App{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <article class="card">
            <div class="card-header">
                <h1 class="mb-0">{{ post.title }}</h1>
                <div class="mt-2">
                    <small class="text-muted">
                        By <strong>{{ post.author.username }}</strong> 
                        on {{ post.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                    </small>
                </div>
            </div>
            <div class="card-body">
                <div class="post-content">
                    {{ post.content|replace('\n', '<br>')|safe }}
                </div>
            </div>
            <div class="card-footer bg-transparent">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <a href="{{ url_for('main.posts') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Posts
                        </a>
                    </div>
                    <div>
                        <small class="text-muted">
                            Author has {{ post.author.posts|length }} post{{ 's' if post.author.posts|length != 1 else '' }}
                        </small>
                    </div>
                </div>
            </div>
        </article>
        
        <!-- Related Posts by Same Author -->
        {% set other_posts = post.author.posts|selectattr('id', 'ne', post.id)|list %}
        {% if other_posts %}
        <div class="mt-4">
            <h4>More posts by {{ post.author.username }}</h4>
            <div class="row">
                {% for other_post in other_posts[:3] %}
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">{{ other_post.title }}</h6>
                            <p class="card-text">{{ other_post.content[:80] }}{% if other_post.content|length > 80 %}...{% endif %}</p>
                            <a href="{{ url_for('main.post_detail', id=other_post.id) }}" class="btn btn-sm btn-outline-primary">Read</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
