"""
Test suite for Flask Demo App
"""

import unittest
import os
import tempfile
from app import create_app, db
from app.models import User, Post


class FlaskDemoTestCase(unittest.TestCase):
    """Base test case for Flask Demo App"""
    
    def setUp(self):
        """Set up test fixtures before each test method"""
        self.db_fd, self.db_path = tempfile.mkstemp()
        
        # Create app with testing configuration
        self.app = create_app('testing')
        self.app.config['SQLALCHEMY_DATABASE_URI'] = f'sqlite:///{self.db_path}'
        self.app.config['TESTING'] = True
        
        self.client = self.app.test_client()
        self.app_context = self.app.app_context()
        self.app_context.push()
        
        # Create database tables
        db.create_all()
        
    def tearDown(self):
        """Clean up after each test method"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
        os.close(self.db_fd)
        os.unlink(self.db_path)
    
    def test_app_exists(self):
        """Test that the app exists"""
        self.assertIsNotNone(self.app)
    
    def test_app_is_testing(self):
        """Test that the app is in testing mode"""
        self.assertTrue(self.app.config['TESTING'])
    
    def test_home_page(self):
        """Test the home page"""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Welcome to Flask Demo!', response.data)
    
    def test_about_page(self):
        """Test the about page"""
        response = self.client.get('/about')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'About Flask Demo App', response.data)
    
    def test_users_page_empty(self):
        """Test users page when no users exist"""
        response = self.client.get('/users')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'No users found', response.data)
    
    def test_posts_page_empty(self):
        """Test posts page when no posts exist"""
        response = self.client.get('/posts')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'No posts found', response.data)
    
    def test_create_user(self):
        """Test creating a new user"""
        response = self.client.post('/users/new', data={
            'username': 'testuser',
            'email': '<EMAIL>'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'User testuser created successfully!', response.data)
        
        # Verify user was created in database
        user = User.query.filter_by(username='testuser').first()
        self.assertIsNotNone(user)
        self.assertEqual(user.email, '<EMAIL>')
    
    def test_create_user_invalid_data(self):
        """Test creating a user with invalid data"""
        response = self.client.post('/users/new', data={
            'username': '',  # Empty username
            'email': 'invalid-email'  # Invalid email
        })
        
        self.assertEqual(response.status_code, 200)
        # Should stay on the form page with errors
        self.assertIn(b'New User', response.data)
    
    def test_create_post(self):
        """Test creating a new post"""
        # First create a user
        user = User(username='testuser', email='<EMAIL>')
        db.session.add(user)
        db.session.commit()
        
        response = self.client.post('/posts/new', data={
            'title': 'Test Post',
            'content': 'This is a test post content that is long enough to pass validation.'
        }, follow_redirects=True)
        
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Post "Test Post" created successfully!', response.data)
        
        # Verify post was created in database
        post = Post.query.filter_by(title='Test Post').first()
        self.assertIsNotNone(post)
        self.assertEqual(post.user_id, user.id)
    
    def test_post_detail(self):
        """Test viewing a single post"""
        # Create user and post
        user = User(username='testuser', email='<EMAIL>')
        db.session.add(user)
        db.session.commit()
        
        post = Post(title='Test Post', content='Test content', user_id=user.id)
        db.session.add(post)
        db.session.commit()
        
        response = self.client.get(f'/posts/{post.id}')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'Test Post', response.data)
        self.assertIn(b'Test content', response.data)
        self.assertIn(b'testuser', response.data)
    
    def test_post_detail_not_found(self):
        """Test viewing a non-existent post"""
        response = self.client.get('/posts/999')
        self.assertEqual(response.status_code, 404)


class ModelTestCase(unittest.TestCase):
    """Test cases for database models"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.app = create_app('testing')
        self.app_context = self.app.app_context()
        self.app_context.push()
        db.create_all()
    
    def tearDown(self):
        """Clean up after tests"""
        db.session.remove()
        db.drop_all()
        self.app_context.pop()
    
    def test_user_model(self):
        """Test User model"""
        user = User(username='testuser', email='<EMAIL>')
        db.session.add(user)
        db.session.commit()
        
        self.assertEqual(user.username, 'testuser')
        self.assertEqual(user.email, '<EMAIL>')
        self.assertIsNotNone(user.created_at)
        self.assertEqual(str(user), '<User testuser>')
    
    def test_post_model(self):
        """Test Post model"""
        user = User(username='testuser', email='<EMAIL>')
        db.session.add(user)
        db.session.commit()
        
        post = Post(title='Test Post', content='Test content', user_id=user.id)
        db.session.add(post)
        db.session.commit()
        
        self.assertEqual(post.title, 'Test Post')
        self.assertEqual(post.content, 'Test content')
        self.assertEqual(post.user_id, user.id)
        self.assertEqual(post.author, user)
        self.assertIsNotNone(post.created_at)
        self.assertEqual(str(post), '<Post Test Post>')
    
    def test_user_post_relationship(self):
        """Test relationship between User and Post"""
        user = User(username='testuser', email='<EMAIL>')
        db.session.add(user)
        db.session.commit()
        
        post1 = Post(title='Post 1', content='Content 1', user_id=user.id)
        post2 = Post(title='Post 2', content='Content 2', user_id=user.id)
        db.session.add_all([post1, post2])
        db.session.commit()
        
        self.assertEqual(len(user.posts), 2)
        self.assertIn(post1, user.posts)
        self.assertIn(post2, user.posts)


if __name__ == '__main__':
    unittest.main()
