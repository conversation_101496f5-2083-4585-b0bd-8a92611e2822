# Flask Demo App 🚀

A comprehensive full-stack Flask application demonstrating modern web development practices, containerized with <PERSON><PERSON> and ready for deployment on Render.

![Flask](https://img.shields.io/badge/Flask-2.3.2-blue)
![Python](https://img.shields.io/badge/Python-3.11-green)
![Docker](https://img.shields.io/badge/Docker-Ready-blue)
![Render](https://img.shields.io/badge/Render-Deploy-purple)

## ✨ Features

### 🏗️ Architecture
- **Application Factory Pattern** - Scalable Flask app structure
- **Blueprint Organization** - Modular route organization
- **Environment-based Configuration** - Development, testing, and production configs
- **Database Integration** - SQLAlchemy ORM with relationships
- **Form Handling** - Flask-WTF with validation

### 🎨 Frontend
- **Responsive Design** - Bootstrap 5 with custom CSS
- **Template Inheritance** - Jinja2 templating with base templates
- **Interactive UI** - JavaScript enhancements and animations
- **Modern Components** - Cards, navigation, forms, and alerts

### 🔧 Development
- **Testing Suite** - Comprehensive tests with pytest
- **Code Coverage** - Coverage reporting and thresholds
- **Environment Variables** - Secure configuration management
- **Development Tools** - Hot reload and debugging

### 🚀 Deployment
- **Docker Support** - Multi-stage builds with security best practices
- **Render Ready** - Configuration for Render deployment
- **Gunicorn WSGI** - Production-ready server
- **Health Checks** - Container health monitoring

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Installation](#installation)
- [Configuration](#configuration)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [API Documentation](#api-documentation)
- [Contributing](#contributing)
- [License](#license)

## 🚀 Quick Start

### Prerequisites
- Python 3.11+
- Docker (optional)
- Git

### 1. Clone the Repository
```bash
git clone <your-repo-url>
cd flaskdemo-main
```

### 2. Setup Development Environment
```bash
# Make setup script executable (Linux/Mac)
chmod +x deploy.sh

# Run setup
./deploy.sh setup

# Or manually:
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 3. Configure Environment
```bash
# Copy example environment file
cp .env.example .env

# Edit .env with your settings
# At minimum, set a SECRET_KEY for security
```

### 4. Run the Application
```bash
# Using the deploy script
./deploy.sh local

# Or manually
python run.py
```

Visit `http://localhost:5000` to see your application! 🎉

## 🛠️ Installation

### Development Setup

1. **Create Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   # or
   venv\Scripts\activate     # Windows
   ```

2. **Install Dependencies**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

3. **Environment Configuration**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

4. **Initialize Database**
   ```bash
   python -c "from app import create_app, db; app = create_app(); app.app_context().push(); db.create_all()"
   ```

### Docker Setup

1. **Build Docker Image**
   ```bash
   docker build -t flask-demo-app .
   ```

2. **Run Container**
   ```bash
   docker run -p 5000:5000 -e SECRET_KEY=your-secret-key flask-demo-app
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database
DATABASE_URL=sqlite:///app.db

# Server
PORT=5000
WEB_CONCURRENCY=4

# Security (Production)
SSL_DISABLE=False
FORCE_HTTPS=True
```

### Configuration Classes

- **DevelopmentConfig** - Debug mode, SQLite database
- **ProductionConfig** - Optimized for production, PostgreSQL
- **TestingConfig** - In-memory database, disabled CSRF
- **RenderConfig** - Render-specific optimizations

## 🔧 Development

### Project Structure
```
flaskdemo-main/
├── app/
│   ├── __init__.py          # Application factory
│   ├── models.py            # Database models
│   ├── routes.py            # Application routes
│   ├── forms.py             # WTF forms
│   ├── static/              # Static assets
│   │   ├── css/
│   │   └── js/
│   └── templates/           # Jinja2 templates
├── tests/                   # Test suite
├── config.py               # Configuration classes
├── requirements.txt        # Python dependencies
├── Dockerfile             # Container configuration
├── render.yaml            # Render deployment config
└── deploy.sh              # Deployment script
```

### Adding New Features

1. **Models** - Add to `app/models.py`
2. **Routes** - Add to `app/routes.py` or create new blueprints
3. **Forms** - Add to `app/forms.py`
4. **Templates** - Add to `app/templates/`
5. **Static Assets** - Add to `app/static/`

### Database Migrations

For production applications, consider using Flask-Migrate:

```bash
pip install Flask-Migrate
flask db init
flask db migrate -m "Initial migration"
flask db upgrade
```

## 🧪 Testing

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app

# Run specific test file
pytest tests/test_app.py

# Run with verbose output
pytest -v

# Run tests and generate HTML coverage report
pytest --cov=app --cov-report=html
```

### Test Structure

- **Unit Tests** - Test individual functions and methods
- **Integration Tests** - Test component interactions
- **Model Tests** - Test database models and relationships
- **Route Tests** - Test HTTP endpoints and responses

### Writing Tests

```python
def test_new_feature(self):
    """Test description"""
    # Arrange
    user = User(username='test', email='<EMAIL>')

    # Act
    result = some_function(user)

    # Assert
    self.assertEqual(result.status, 'success')
```

## 🚀 Deployment

### Render Deployment

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Connect to Render**
   - Visit [render.com](https://render.com)
   - Connect your GitHub repository
   - Render will automatically detect `render.yaml`

3. **Environment Variables**
   Set these in Render dashboard:
   ```
   SECRET_KEY=your-production-secret-key
   FLASK_ENV=production
   ```

4. **Database Setup**
   - Render will create PostgreSQL database automatically
   - Connection string is provided via `DATABASE_URL`

### Docker Deployment

```bash
# Build production image
docker build -t flask-demo-app .

# Run container
docker run -d \
  -p 5000:5000 \
  -e SECRET_KEY=your-secret-key \
  -e DATABASE_URL=your-database-url \
  flask-demo-app
```

### Manual Deployment

```bash
# Install dependencies
pip install -r requirements.txt

# Set environment variables
export FLASK_ENV=production
export SECRET_KEY=your-secret-key

# Run with Gunicorn
gunicorn --config gunicorn.conf.py run:app
```

## 📚 API Documentation

### Routes

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Home page with recent posts |
| GET | `/about` | About page |
| GET | `/users` | List all users |
| GET | `/users/new` | New user form |
| POST | `/users/new` | Create new user |
| GET | `/posts` | List all posts |
| GET | `/posts/new` | New post form |
| POST | `/posts/new` | Create new post |
| GET | `/posts/<id>` | View single post |

### Models

#### User Model
```python
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    posts = db.relationship('Post', backref='author', lazy=True)
```

#### Post Model
```python
class Post(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes**
4. **Add tests for new functionality**
5. **Run tests**
   ```bash
   pytest
   ```
6. **Commit your changes**
   ```bash
   git commit -m "Add amazing feature"
   ```
7. **Push to the branch**
   ```bash
   git push origin feature/amazing-feature
   ```
8. **Open a Pull Request**

### Development Guidelines

- Follow PEP 8 style guidelines
- Write tests for new features
- Update documentation
- Use meaningful commit messages
- Keep functions small and focused

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Flask](https://flask.palletsprojects.com/) - The web framework
- [Bootstrap](https://getbootstrap.com/) - CSS framework
- [Render](https://render.com/) - Hosting platform
- [Docker](https://www.docker.com/) - Containerization

## 📞 Support

If you have any questions or need help:

1. Check the [documentation](#table-of-contents)
2. Search [existing issues](../../issues)
3. Create a [new issue](../../issues/new)

---

**Happy coding!** 🎉