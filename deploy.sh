#!/bin/bash

# Flask Demo App Deployment Script
# This script helps deploy the Flask application to various platforms

set -e

echo "🚀 Flask Demo App Deployment Script"
echo "=================================="

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to deploy to Render
deploy_render() {
    echo "📦 Deploying to Render..."
    
    if [ ! -f "render.yaml" ]; then
        echo "❌ render.yaml not found. Please ensure it exists."
        exit 1
    fi
    
    echo "✅ render.yaml found"
    echo "📋 To deploy to Render:"
    echo "   1. Push your code to GitHub"
    echo "   2. Connect your GitHub repo to Render"
    echo "   3. Render will automatically deploy using render.yaml"
    echo "   4. Set environment variables in Render dashboard"
    echo ""
    echo "🔗 Visit: https://render.com to get started"
}

# Function to build Docker image
build_docker() {
    echo "🐳 Building Docker image..."
    
    if ! command_exists docker; then
        echo "❌ Docker not found. Please install Docker first."
        exit 1
    fi
    
    IMAGE_NAME="flask-demo-app"
    TAG=${1:-latest}
    
    echo "Building image: $IMAGE_NAME:$TAG"
    docker build -t "$IMAGE_NAME:$TAG" .
    
    echo "✅ Docker image built successfully!"
    echo "🏃 To run locally: docker run -p 5000:5000 $IMAGE_NAME:$TAG"
}

# Function to run locally
run_local() {
    echo "🏠 Running Flask app locally..."
    
    if [ ! -f ".env" ]; then
        echo "⚠️  .env file not found. Creating default..."
        cp .env.example .env 2>/dev/null || echo "FLASK_ENV=development" > .env
    fi
    
    if ! command_exists python; then
        echo "❌ Python not found. Please install Python first."
        exit 1
    fi
    
    # Install dependencies
    echo "📦 Installing dependencies..."
    pip install -r requirements.txt
    
    # Run the application
    echo "🚀 Starting Flask development server..."
    python run.py
}

# Function to run tests
run_tests() {
    echo "🧪 Running tests..."
    
    if [ -f "tests/test_app.py" ]; then
        python -m pytest tests/ -v
    else
        echo "⚠️  No tests found. Consider adding tests in tests/ directory."
    fi
}

# Function to setup development environment
setup_dev() {
    echo "🛠️  Setting up development environment..."
    
    # Create virtual environment if it doesn't exist
    if [ ! -d "venv" ]; then
        echo "📦 Creating virtual environment..."
        python -m venv venv
    fi
    
    # Activate virtual environment
    echo "🔄 Activating virtual environment..."
    source venv/bin/activate || source venv/Scripts/activate
    
    # Install dependencies
    echo "📦 Installing dependencies..."
    pip install --upgrade pip
    pip install -r requirements.txt
    
    # Create .env if it doesn't exist
    if [ ! -f ".env" ]; then
        echo "📝 Creating .env file..."
        cat > .env << EOF
FLASK_ENV=development
SECRET_KEY=dev-secret-key-change-in-production
DATABASE_URL=sqlite:///app.db
EOF
    fi
    
    echo "✅ Development environment setup complete!"
    echo "🏃 Run 'source venv/bin/activate' to activate the environment"
    echo "🚀 Run 'python run.py' to start the development server"
}

# Main script logic
case "${1:-help}" in
    "render")
        deploy_render
        ;;
    "docker")
        build_docker "$2"
        ;;
    "local")
        run_local
        ;;
    "test")
        run_tests
        ;;
    "setup")
        setup_dev
        ;;
    "help"|*)
        echo "Usage: $0 {render|docker|local|test|setup|help}"
        echo ""
        echo "Commands:"
        echo "  render  - Deploy to Render (shows instructions)"
        echo "  docker  - Build Docker image (optional tag as 2nd arg)"
        echo "  local   - Run Flask app locally"
        echo "  test    - Run tests"
        echo "  setup   - Setup development environment"
        echo "  help    - Show this help message"
        echo ""
        echo "Examples:"
        echo "  $0 setup          # Setup development environment"
        echo "  $0 local          # Run locally"
        echo "  $0 docker v1.0    # Build Docker image with tag v1.0"
        echo "  $0 render         # Show Render deployment instructions"
        ;;
esac
