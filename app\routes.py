from flask import Blueprint, render_template, request, redirect, url_for, flash
from app import db
from app.models import User, Post
from app.forms import UserForm, PostForm

# Create blueprint
main = Blueprint('main', __name__)

@main.route('/')
def home():
    """Home page showing recent posts"""
    posts = Post.query.order_by(Post.created_at.desc()).limit(5).all()
    return render_template('index.html', posts=posts)

@main.route('/about')
def about():
    """About page"""
    return render_template('about.html')

@main.route('/users')
def users():
    """Display all users"""
    users = User.query.all()
    return render_template('users.html', users=users)

@main.route('/users/new', methods=['GET', 'POST'])
def new_user():
    """Create a new user"""
    form = UserForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, email=form.email.data)
        db.session.add(user)
        db.session.commit()
        flash(f'User {user.username} created successfully!', 'success')
        return redirect(url_for('main.users'))
    return render_template('user_form.html', form=form, title='New User')

@main.route('/posts')
def posts():
    """Display all posts"""
    posts = Post.query.order_by(Post.created_at.desc()).all()
    return render_template('posts.html', posts=posts)

@main.route('/posts/new', methods=['GET', 'POST'])
def new_post():
    """Create a new post"""
    form = PostForm()
    users = User.query.all()

    if form.validate_on_submit():
        # For demo purposes, assign to first user or create one
        user = users[0] if users else User(username='demo', email='<EMAIL>')
        if not users:
            db.session.add(user)
            db.session.commit()

        post = Post(title=form.title.data, content=form.content.data, user_id=user.id)
        db.session.add(post)
        db.session.commit()
        flash(f'Post "{post.title}" created successfully!', 'success')
        return redirect(url_for('main.posts'))

    return render_template('post_form.html', form=form, title='New Post')

@main.route('/posts/<int:id>')
def post_detail(id):
    """Display a single post"""
    post = Post.query.get_or_404(id)
    return render_template('post_detail.html', post=post)
