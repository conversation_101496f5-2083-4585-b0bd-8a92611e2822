from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from app import db
from app.models import User, Post
from app.forms import UserForm, PostForm, AITextGenerationForm
from app.ai_service import get_ai_service
import logging

logger = logging.getLogger(__name__)

# Create blueprint
main = Blueprint('main', __name__)

@main.route('/')
def home():
    """Home page showing recent posts"""
    posts = Post.query.order_by(Post.created_at.desc()).limit(5).all()
    return render_template('index.html', posts=posts)

@main.route('/about')
def about():
    """About page"""
    return render_template('about.html')

@main.route('/users')
def users():
    """Display all users"""
    users = User.query.all()
    return render_template('users.html', users=users)

@main.route('/users/new', methods=['GET', 'POST'])
def new_user():
    """Create a new user"""
    form = UserForm()
    if form.validate_on_submit():
        user = User(username=form.username.data, email=form.email.data)
        db.session.add(user)
        db.session.commit()
        flash(f'User {user.username} created successfully!', 'success')
        return redirect(url_for('main.users'))
    return render_template('user_form.html', form=form, title='New User')

@main.route('/posts')
def posts():
    """Display all posts"""
    posts = Post.query.order_by(Post.created_at.desc()).all()
    return render_template('posts.html', posts=posts)

@main.route('/posts/new', methods=['GET', 'POST'])
def new_post():
    """Create a new post"""
    form = PostForm()
    users = User.query.all()

    if form.validate_on_submit():
        # For demo purposes, assign to first user or create one
        user = users[0] if users else User(username='demo', email='<EMAIL>')
        if not users:
            db.session.add(user)
            db.session.commit()

        post = Post(title=form.title.data, content=form.content.data, user_id=user.id)
        db.session.add(post)
        db.session.commit()
        flash(f'Post "{post.title}" created successfully!', 'success')
        return redirect(url_for('main.posts'))

    return render_template('post_form.html', form=form, title='New Post')

@main.route('/posts/<int:id>')
def post_detail(id):
    """Display a single post"""
    post = Post.query.get_or_404(id)
    return render_template('post_detail.html', post=post)

# AI Routes
@main.route('/ai')
def ai_home():
    """AI text generation home page"""
    ai_service = get_ai_service()
    model_info = ai_service.get_model_info()
    return render_template('ai/home.html', model_info=model_info)

@main.route('/ai/generate', methods=['GET', 'POST'])
def ai_generate():
    """AI text generation interface"""
    form = AITextGenerationForm()
    ai_service = get_ai_service()

    if not ai_service.is_available():
        flash('AI service is not available. Please check configuration.', 'error')
        return redirect(url_for('main.ai_home'))

    result = None
    if form.validate_on_submit():
        try:
            result = ai_service.generate_text(
                prompt=form.prompt.data,
                max_length=form.max_length.data,
                temperature=form.temperature.data
            )

            if result['success']:
                flash('Text generated successfully!', 'success')
            else:
                flash(f'Generation failed: {result.get("error", "Unknown error")}', 'error')

        except Exception as e:
            logger.error(f"AI generation error: {str(e)}")
            flash('An error occurred during text generation.', 'error')

    return render_template('ai/generate.html', form=form, result=result)

@main.route('/ai/api/generate', methods=['POST'])
def ai_api_generate():
    """API endpoint for AI text generation"""
    ai_service = get_ai_service()

    if not ai_service.is_available():
        return jsonify({
            'success': False,
            'error': 'AI service not available'
        }), 503

    try:
        data = request.get_json()
        if not data or 'prompt' not in data:
            return jsonify({
                'success': False,
                'error': 'Prompt is required'
            }), 400

        result = ai_service.generate_text(
            prompt=data['prompt'],
            max_length=data.get('max_length', 200),
            temperature=data.get('temperature', 0.7)
        )

        return jsonify(result)

    except Exception as e:
        logger.error(f"AI API error: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Internal server error'
        }), 500

@main.route('/ai/status')
def ai_status():
    """Get AI service status"""
    ai_service = get_ai_service()
    model_info = ai_service.get_model_info()

    return jsonify({
        'available': ai_service.is_available(),
        'loaded': ai_service.is_loaded,
        'model_info': model_info
    })

@main.route('/streamlit')
def streamlit_redirect():
    """Redirect to Streamlit app"""
    import os
    streamlit_port = os.environ.get('STREAMLIT_PORT', '8501')
    streamlit_url = f"http://localhost:{streamlit_port}"
    return redirect(streamlit_url)
