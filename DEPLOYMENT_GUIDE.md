# Flask Demo App - Deployment Guide 🚀

This guide provides step-by-step instructions for deploying your Flask application to various platforms.

## 📋 Pre-Deployment Checklist

- [ ] All tests pass (`pytest`)
- [ ] Environment variables configured
- [ ] Database migrations ready (if using Flask-Migrate)
- [ ] Static files optimized
- [ ] Security settings reviewed
- [ ] Backup strategy in place

## 🌐 Render Deployment (Recommended)

### Step 1: Prepare Your Repository

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Verify render.yaml**
   - Ensure `render.yaml` is in your repository root
   - Check service configuration matches your needs

### Step 2: Deploy on Render

1. **Create Render Account**
   - Visit [render.com](https://render.com)
   - Sign up with GitHub

2. **Connect Repository**
   - Click "New +" → "Web Service"
   - Connect your GitHub repository
   - Render auto-detects `render.yaml`

3. **Configure Environment Variables**
   ```
   SECRET_KEY=your-production-secret-key-here
   FLASK_ENV=production
   ```

4. **Deploy**
   - Click "Create Web Service"
   - Render automatically builds and deploys

### Step 3: Database Setup

1. **PostgreSQL Database**
   - Render creates database automatically
   - Connection string provided via `DATABASE_URL`

2. **Initialize Database**
   - Database tables are created automatically on first run
   - For migrations, add Flask-Migrate commands to build script

## 🐳 Docker Deployment

### Local Docker Testing

```bash
# Build image
docker build -t flask-demo-app .

# Run container
docker run -p 5000:5000 \
  -e SECRET_KEY=your-secret-key \
  -e DATABASE_URL=sqlite:///app.db \
  flask-demo-app
```

### Production Docker

```bash
# Build production image
docker build -t flask-demo-app:prod .

# Run with production settings
docker run -d \
  --name flask-demo \
  -p 80:5000 \
  -e SECRET_KEY=your-production-secret \
  -e DATABASE_URL=postgresql://... \
  -e FLASK_ENV=production \
  --restart unless-stopped \
  flask-demo-app:prod
```

## ☁️ Cloud Platform Deployment

### Heroku

1. **Install Heroku CLI**
2. **Create Heroku App**
   ```bash
   heroku create your-app-name
   ```
3. **Add PostgreSQL**
   ```bash
   heroku addons:create heroku-postgresql:hobby-dev
   ```
4. **Set Environment Variables**
   ```bash
   heroku config:set SECRET_KEY=your-secret-key
   heroku config:set FLASK_ENV=production
   ```
5. **Deploy**
   ```bash
   git push heroku main
   ```

### AWS/GCP/Azure

- Use Docker containers with their container services
- Configure load balancers and auto-scaling
- Set up managed databases (RDS, Cloud SQL, etc.)
- Configure environment variables through their consoles

## 🔧 Production Configuration

### Environment Variables

**Required:**
```env
SECRET_KEY=your-production-secret-key
FLASK_ENV=production
DATABASE_URL=postgresql://user:pass@host:port/db
```

**Optional:**
```env
WEB_CONCURRENCY=4
LOG_LEVEL=info
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

### Security Checklist

- [ ] Strong SECRET_KEY (use `python -c "import secrets; print(secrets.token_hex(32))"`)
- [ ] HTTPS enabled
- [ ] Database credentials secure
- [ ] Debug mode disabled (`FLASK_ENV=production`)
- [ ] Error logging configured
- [ ] Rate limiting implemented (if needed)
- [ ] CORS configured properly (if needed)

## 📊 Monitoring & Maintenance

### Health Checks

The application includes a health check endpoint at `/` that returns 200 OK.

### Logging

- Production logs are configured in `config.py`
- Logs are written to stdout for cloud platforms
- File logging available for traditional servers

### Database Backups

**Render:**
- Automatic daily backups included
- Manual backups available in dashboard

**Self-hosted:**
```bash
# PostgreSQL backup
pg_dump $DATABASE_URL > backup.sql

# Restore
psql $DATABASE_URL < backup.sql
```

### Updates

1. **Test locally**
2. **Run tests** (`pytest`)
3. **Deploy to staging** (if available)
4. **Deploy to production**
5. **Monitor logs** for errors
6. **Verify functionality**

## 🚨 Troubleshooting

### Common Issues

**Application won't start:**
- Check environment variables
- Verify database connection
- Check logs for errors

**Database errors:**
- Verify DATABASE_URL format
- Check database permissions
- Ensure database exists

**Static files not loading:**
- Check file paths in templates
- Verify static file configuration
- Check web server configuration

### Debug Commands

```bash
# Check environment
env | grep FLASK

# Test database connection
python -c "from app import create_app, db; app = create_app(); app.app_context().push(); print('DB OK' if db.engine.execute('SELECT 1').scalar() == 1 else 'DB Error')"

# Check application factory
python -c "from app import create_app; print('App created:', create_app())"
```

## 📞 Support

If you encounter issues:

1. Check the [main README](README.md)
2. Review application logs
3. Test locally first
4. Check platform-specific documentation
5. Create an issue in the repository

---

**Happy deploying!** 🎉
