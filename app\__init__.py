import os
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from config import config

# Initialize extensions
db = SQLAlchemy()

def create_app(config_name=None):
    """Application factory pattern"""
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'default')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # Initialize extensions with app
    db.init_app(app)

    # Register blueprints
    from .routes import main
    app.register_blueprint(main)

    # Create database tables
    with app.app_context():
        db.create_all()

        # Initialize AI service
        try:
            from .ai_service import init_ai_service
            init_ai_service(app)
        except ImportError as e:
            app.logger.warning(f"AI service not available: {e}")
        except Exception as e:
            app.logger.error(f"Failed to initialize AI service: {e}")

    return app
