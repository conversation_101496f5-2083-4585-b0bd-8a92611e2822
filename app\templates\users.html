{% extends "base.html" %}

{% block title %}Users - Flask Demo App{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Users</h1>
    <a href="{{ url_for('main.new_user') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Add New User
    </a>
</div>

{% if users %}
    <div class="row">
        {% for user in users %}
        <div class="col-md-6 col-lg-4 mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-user"></i> {{ user.username }}
                    </h5>
                    <p class="card-text">
                        <strong>Email:</strong> {{ user.email }}<br>
                        <strong>Joined:</strong> {{ user.created_at.strftime('%B %d, %Y') }}<br>
                        <strong>Posts:</strong> {{ user.posts|length }}
                    </p>
                    {% if user.posts %}
                        <div class="mt-2">
                            <small class="text-muted">Recent posts:</small>
                            <ul class="list-unstyled mt-1">
                                {% for post in user.posts[:2] %}
                                <li>
                                    <a href="{{ url_for('main.post_detail', id=post.id) }}" class="text-decoration-none">
                                        {{ post.title }}
                                    </a>
                                </li>
                                {% endfor %}
                                {% if user.posts|length > 2 %}
                                <li><small class="text-muted">... and {{ user.posts|length - 2 }} more</small></li>
                                {% endif %}
                            </ul>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <div class="mt-4">
        <p class="text-muted">Total users: {{ users|length }}</p>
    </div>
{% else %}
    <div class="alert alert-info text-center">
        <h4>No users found</h4>
        <p>Be the first to create a user account!</p>
        <a href="{{ url_for('main.new_user') }}" class="btn btn-primary">Create First User</a>
    </div>
{% endif %}

<div class="mt-4">
    <a href="{{ url_for('main.home') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to Home
    </a>
</div>
{% endblock %}
