from datetime import datetime
from app import db

class User(db.Model):
    """User model for the application"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationship with posts
    posts = db.relationship('Post', backref='author', lazy=True)

    def __repr__(self):
        return f'<User {self.username}>'

class Post(db.Model):
    """Post model for blog-like functionality"""
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(100), nullable=False)
    content = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.<PERSON>umn(db.<PERSON>, db.<PERSON><PERSON><PERSON>('user.id'), nullable=False)

    def __repr__(self):
        return f'<Post {self.title}>'