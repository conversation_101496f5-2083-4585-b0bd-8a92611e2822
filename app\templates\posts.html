{% extends "base.html" %}

{% block title %}Posts - Flask Demo App{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>All Posts</h1>
    <a href="{{ url_for('main.new_post') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> Create New Post
    </a>
</div>

{% if posts %}
    <div class="row">
        {% for post in posts %}
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ post.title }}</h5>
                    <p class="card-text">{{ post.content[:150] }}{% if post.content|length > 150 %}...{% endif %}</p>
                </div>
                <div class="card-footer bg-transparent">
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            By <strong>{{ post.author.username }}</strong><br>
                            {{ post.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                        </small>
                        <a href="{{ url_for('main.post_detail', id=post.id) }}" class="btn btn-outline-primary btn-sm">
                            Read More
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <div class="mt-4">
        <p class="text-muted">Total posts: {{ posts|length }}</p>
    </div>
{% else %}
    <div class="alert alert-info text-center">
        <h4>No posts found</h4>
        <p>Be the first to create a post!</p>
        <a href="{{ url_for('main.new_post') }}" class="btn btn-primary">Create First Post</a>
    </div>
{% endif %}

<div class="mt-4">
    <a href="{{ url_for('main.home') }}" class="btn btn-outline-secondary">
        <i class="fas fa-arrow-left"></i> Back to Home
    </a>
</div>
{% endblock %}
