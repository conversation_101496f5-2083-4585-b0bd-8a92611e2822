# Production Environment Configuration
# Copy this to .env and update values for production deployment

# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=production
SECRET_KEY=CHANGE-THIS-TO-A-RANDOM-SECRET-KEY

# Database Configuration (PostgreSQL for production)
DATABASE_URL=postgresql://username:password@hostname:port/database

# Server Configuration
PORT=5000
WEB_CONCURRENCY=4

# Logging
LOG_LEVEL=info

# Security
SSL_DISABLE=False
FORCE_HTTPS=True

# Performance
SQLALCHEMY_ENGINE_OPTIONS={"pool_pre_ping": true, "pool_recycle": 300}

# Optional: External Services
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=True
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# Optional: Redis (for caching/sessions)
# REDIS_URL=redis://hostname:port/0

# Optional: Monitoring
# SENTRY_DSN=https://<EMAIL>/project-id
