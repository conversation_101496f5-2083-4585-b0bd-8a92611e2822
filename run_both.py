#!/usr/bin/env python3
"""
Script to run both Flask and Streamlit applications simultaneously
"""

import os
import sys
import subprocess
import threading
import time
import signal
from pathlib import Path

def run_flask():
    """Run the Flask application"""
    print("🚀 Starting Flask application...")
    try:
        # Set environment variables for Flask
        env = os.environ.copy()
        env['FLASK_APP'] = 'run.py'
        env['FLASK_ENV'] = 'development'
        
        # Run Flask
        subprocess.run([
            sys.executable, 'run.py'
        ], env=env, cwd=Path(__file__).parent)
    except KeyboardInterrupt:
        print("🛑 Flask application stopped")
    except Exception as e:
        print(f"❌ Flask error: {e}")

def run_streamlit():
    """Run the Streamlit application"""
    print("🚀 Starting Streamlit application...")
    try:
        # Wait a moment for Flask to start
        time.sleep(3)
        
        # Set environment variables for Streamlit
        env = os.environ.copy()
        streamlit_port = env.get('STREAMLIT_PORT', '8501')
        
        # Run Streamlit
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 
            'app/app.py',
            '--server.port', streamlit_port,
            '--server.address', 'localhost',
            '--browser.gatherUsageStats', 'false'
        ], env=env, cwd=Path(__file__).parent)
    except KeyboardInterrupt:
        print("🛑 Streamlit application stopped")
    except Exception as e:
        print(f"❌ Streamlit error: {e}")

def signal_handler(signum, frame):
    """Handle Ctrl+C gracefully"""
    print("\n🛑 Shutting down applications...")
    sys.exit(0)

def main():
    """Main function to run both applications"""
    print("🤖 Flask + Streamlit AI Demo Launcher")
    print("=" * 50)
    
    # Check if required files exist
    required_files = ['run.py', 'app/app.py']
    for file_path in required_files:
        if not Path(file_path).exists():
            print(f"❌ Required file not found: {file_path}")
            sys.exit(1)
    
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    # Create threads for both applications
    flask_thread = threading.Thread(target=run_flask, daemon=True)
    streamlit_thread = threading.Thread(target=run_streamlit, daemon=True)
    
    try:
        # Start Flask
        flask_thread.start()
        print("✅ Flask thread started")
        
        # Start Streamlit
        streamlit_thread.start()
        print("✅ Streamlit thread started")
        
        print("\n🌐 Applications starting...")
        print("📱 Flask App: http://localhost:5000")
        print("🎯 Streamlit App: http://localhost:8501")
        print("\n💡 Press Ctrl+C to stop both applications")
        
        # Keep the main thread alive
        while True:
            time.sleep(1)
            
            # Check if threads are still alive
            if not flask_thread.is_alive():
                print("⚠️  Flask thread died")
            if not streamlit_thread.is_alive():
                print("⚠️  Streamlit thread died")
                
    except KeyboardInterrupt:
        print("\n🛑 Received shutdown signal")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        print("👋 Goodbye!")

if __name__ == "__main__":
    main()
