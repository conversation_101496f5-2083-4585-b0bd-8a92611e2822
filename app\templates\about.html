{% extends "base.html" %}

{% block title %}About - Flask Demo App{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8 mx-auto">
        <h1>About Flask Demo App</h1>
        
        <p class="lead">This is a comprehensive Flask application demonstrating modern web development practices and deployment strategies.</p>
        
        <h2>Technology Stack</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>Backend</h4>
                <ul>
                    <li><strong>Flask</strong> - Python web framework</li>
                    <li><strong>SQLAlchemy</strong> - Database ORM</li>
                    <li><strong>Flask-WTF</strong> - Form handling</li>
                    <li><strong>Gunicorn</strong> - WSGI server</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4>Frontend</h4>
                <ul>
                    <li><strong>Jinja2</strong> - Template engine</li>
                    <li><strong>Bootstrap 5</strong> - CSS framework</li>
                    <li><strong>Font Awesome</strong> - Icons</li>
                    <li><strong>Custom CSS/JS</strong> - Additional styling</li>
                </ul>
            </div>
        </div>
        
        <h2>Deployment</h2>
        <div class="row">
            <div class="col-md-6">
                <h4>Containerization</h4>
                <ul>
                    <li><strong>Docker</strong> - Container platform</li>
                    <li><strong>Multi-stage builds</strong> - Optimized images</li>
                    <li><strong>Environment variables</strong> - Configuration</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h4>Hosting</h4>
                <ul>
                    <li><strong>Render</strong> - Cloud platform</li>
                    <li><strong>PostgreSQL</strong> - Production database</li>
                    <li><strong>Automatic deployments</strong> - CI/CD</li>
                </ul>
            </div>
        </div>
        
        <h2>Features</h2>
        <div class="alert alert-info">
            <h4>Application Features</h4>
            <ul class="mb-0">
                <li>User management with CRUD operations</li>
                <li>Blog-style post creation and viewing</li>
                <li>Form validation and error handling</li>
                <li>Responsive design for mobile and desktop</li>
                <li>Flash messaging for user feedback</li>
                <li>Database relationships and queries</li>
            </ul>
        </div>
        
        <div class="alert alert-success">
            <h4>Development Features</h4>
            <ul class="mb-0">
                <li>Application factory pattern</li>
                <li>Blueprint organization</li>
                <li>Environment-based configuration</li>
                <li>Database migrations support</li>
                <li>Error handling and logging</li>
                <li>Testing framework ready</li>
            </ul>
        </div>
        
        <div class="text-center mt-4">
            <a href="{{ url_for('main.home') }}" class="btn btn-primary">Back to Home</a>
            <a href="{{ url_for('main.users') }}" class="btn btn-outline-primary">View Users</a>
            <a href="{{ url_for('main.posts') }}" class="btn btn-outline-secondary">View Posts</a>
        </div>
    </div>
</div>
{% endblock %}
