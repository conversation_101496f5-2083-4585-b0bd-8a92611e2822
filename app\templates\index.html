{% extends "base.html" %}

{% block title %}Home - Flask Demo App{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-8">
        <div class="jumbotron bg-primary text-white p-5 rounded mb-4">
            <h1 class="display-4">Welcome to Flask Demo!</h1>
            <p class="lead">A full-stack Flask application with templating, static assets, containerized with Docker, and ready for deployment on Render.</p>
            <hr class="my-4">
            <p>This application demonstrates Flask best practices including blueprints, SQLAlchemy, forms, and responsive design.</p>
            <a class="btn btn-light btn-lg" href="{{ url_for('main.about') }}" role="button">Learn More</a>
        </div>

        <h2>Recent Posts</h2>
        {% if posts %}
            <div class="row">
                {% for post in posts %}
                <div class="col-md-6 mb-3">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">{{ post.title }}</h5>
                            <p class="card-text">{{ post.content[:100] }}{% if post.content|length > 100 %}...{% endif %}</p>
                            <p class="card-text">
                                <small class="text-muted">
                                    By {{ post.author.username }} on {{ post.created_at.strftime('%B %d, %Y') }}
                                </small>
                            </p>
                            <a href="{{ url_for('main.post_detail', id=post.id) }}" class="btn btn-primary btn-sm">Read More</a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            <div class="text-center mt-3">
                <a href="{{ url_for('main.posts') }}" class="btn btn-outline-primary">View All Posts</a>
            </div>
        {% else %}
            <div class="alert alert-info">
                <h4>No posts yet!</h4>
                <p>Be the first to create a post.</p>
                <a href="{{ url_for('main.new_post') }}" class="btn btn-primary">Create Post</a>
            </div>
        {% endif %}
    </div>

    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('main.new_user') }}" class="btn btn-success">Add New User</a>
                    <a href="{{ url_for('main.new_post') }}" class="btn btn-info">Create New Post</a>
                    <a href="{{ url_for('main.ai_generate') }}" class="btn btn-warning">
                        <i class="fas fa-robot"></i> AI Text Generation
                    </a>
                    <a href="{{ url_for('main.users') }}" class="btn btn-outline-primary">View All Users</a>
                    <a href="{{ url_for('main.posts') }}" class="btn btn-outline-secondary">View All Posts</a>
                </div>
            </div>
        </div>

        <div class="card mt-3">
            <div class="card-header">
                <h5>Features</h5>
            </div>
            <div class="card-body">
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> Flask Application Factory</li>
                    <li><i class="fas fa-check text-success"></i> SQLAlchemy Database</li>
                    <li><i class="fas fa-check text-success"></i> Flask-WTF Forms</li>
                    <li><i class="fas fa-check text-success"></i> Bootstrap UI</li>
                    <li><i class="fas fa-check text-success"></i> BLOOMZ-560M AI Model</li>
                    <li><i class="fas fa-check text-success"></i> Streamlit Integration</li>
                    <li><i class="fas fa-check text-success"></i> Docker Container</li>
                    <li><i class="fas fa-check text-success"></i> Render Deployment</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
