{% extends "base.html" %}

{% block title %}AI Text Generation - Flask Demo App{% endblock %}

{% block head %}
<style>
.generation-result {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-left: 4px solid #007bff;
    padding: 1.5rem;
    border-radius: 0.5rem;
    margin-top: 1rem;
}

.prompt-text {
    background-color: #e3f2fd;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 3px solid #2196f3;
    margin-bottom: 1rem;
}

.generated-text {
    background-color: #f3e5f5;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 3px solid #9c27b0;
    white-space: pre-wrap;
    font-family: 'Courier New', monospace;
    line-height: 1.6;
}

.parameter-badge {
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.loading-spinner {
    display: none;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 mx-auto">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-robot"></i> AI Text Generation</h1>
            <a href="{{ url_for('main.ai_home') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to AI Home
            </a>
        </div>

        <div class="row">
            <!-- Generation Form -->
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-edit"></i> Text Generation</h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" id="generation-form">
                            {{ form.hidden_tag() }}
                            
                            <div class="mb-3">
                                {{ form.prompt.label(class="form-label") }}
                                {{ form.prompt(class="form-control" + (" is-invalid" if form.prompt.errors else "")) }}
                                {% if form.prompt.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.prompt.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">Enter a text prompt to generate continuation.</div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.max_length.label(class="form-label") }}
                                        {{ form.max_length(class="form-control" + (" is-invalid" if form.max_length.errors else "")) }}
                                        {% if form.max_length.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.max_length.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">Maximum length of generated text (50-500)</div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.temperature.label(class="form-label") }}
                                        {{ form.temperature(class="form-control" + (" is-invalid" if form.temperature.errors else ""), step="0.1") }}
                                        {% if form.temperature.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.temperature.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                        <div class="form-text">Creativity level (0.1=conservative, 1.5=creative)</div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                {{ form.submit(class="btn btn-primary", id="generate-btn") }}
                                <div class="loading-spinner text-center mt-2">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Generating...</span>
                                    </div>
                                    <p class="mt-2">Generating text... This may take a moment.</p>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Quick Examples -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-lightbulb"></i> Example Prompts</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-info btn-sm example-prompt" data-prompt="Once upon a time in a magical forest,">
                                Story Beginning
                            </button>
                            <button class="btn btn-outline-info btn-sm example-prompt" data-prompt="The future of artificial intelligence will">
                                Tech Prediction
                            </button>
                            <button class="btn btn-outline-info btn-sm example-prompt" data-prompt="In a world where robots and humans coexist,">
                                Sci-Fi Scenario
                            </button>
                            <button class="btn btn-outline-info btn-sm example-prompt" data-prompt="The recipe for happiness includes">
                                Philosophical
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Results -->
            <div class="col-lg-6">
                {% if result %}
                <div class="card">
                    <div class="card-header">
                        <h5>
                            <i class="fas fa-{{ 'check-circle text-success' if result.success else 'exclamation-triangle text-danger' }}"></i>
                            Generation Result
                        </h5>
                    </div>
                    <div class="card-body">
                        {% if result.success %}
                            <div class="prompt-text">
                                <strong>Prompt:</strong><br>
                                {{ result.prompt }}
                            </div>
                            
                            <div class="generated-text">
                                <strong>Generated Text:</strong><br>
                                {{ result.generated_text }}
                            </div>
                            
                            {% if result.new_text %}
                            <div class="mt-3">
                                <strong>New Content Only:</strong>
                                <div class="generated-text">{{ result.new_text }}</div>
                            </div>
                            {% endif %}
                            
                            <div class="mt-3">
                                <strong>Parameters:</strong><br>
                                <span class="badge bg-primary parameter-badge">Length: {{ result.parameters.max_length }}</span>
                                <span class="badge bg-secondary parameter-badge">Temperature: {{ result.parameters.temperature }}</span>
                                <span class="badge bg-info parameter-badge">Top-p: {{ result.parameters.top_p }}</span>
                                <span class="badge bg-success parameter-badge">Top-k: {{ result.parameters.top_k }}</span>
                            </div>
                        {% else %}
                            <div class="alert alert-danger">
                                <strong>Generation Failed:</strong><br>
                                {{ result.error }}
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% else %}
                <div class="card">
                    <div class="card-body text-center text-muted">
                        <i class="fas fa-robot fa-3x mb-3"></i>
                        <p>Enter a prompt and click "Generate Text" to see AI-generated content here.</p>
                    </div>
                </div>
                {% endif %}

                <!-- API Example -->
                <div class="card mt-3">
                    <div class="card-header">
                        <h6><i class="fas fa-code"></i> API Usage</h6>
                    </div>
                    <div class="card-body">
                        <p class="small">You can also use the API endpoint:</p>
                        <pre class="bg-light p-2 rounded"><code>POST /ai/api/generate
{
  "prompt": "Your text here",
  "max_length": 200,
  "temperature": 0.7
}</code></pre>
                        <button class="btn btn-sm btn-outline-primary" onclick="testAPI()">Test API</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Handle form submission with loading state
document.getElementById('generation-form').addEventListener('submit', function() {
    const submitBtn = document.getElementById('generate-btn');
    const spinner = document.querySelector('.loading-spinner');
    
    submitBtn.style.display = 'none';
    spinner.style.display = 'block';
});

// Example prompt buttons
document.querySelectorAll('.example-prompt').forEach(button => {
    button.addEventListener('click', function() {
        const prompt = this.getAttribute('data-prompt');
        document.getElementById('prompt').value = prompt;
    });
});

// Test API function
function testAPI() {
    const prompt = document.getElementById('prompt').value || "Hello, world!";
    
    fetch('/ai/api/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            prompt: prompt,
            max_length: 100,
            temperature: 0.7
        })
    })
    .then(response => response.json())
    .then(data => {
        alert('API Response: ' + (data.success ? data.generated_text : data.error));
    })
    .catch(error => {
        alert('API Error: ' + error);
    });
}
</script>
{% endblock %}
