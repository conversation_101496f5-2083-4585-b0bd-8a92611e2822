# Flask Configuration
FLASK_APP=run.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-change-in-production

# Database Configuration
DATABASE_URL=sqlite:///app.db
DEV_DATABASE_URL=sqlite:///app.db

# Production Database (PostgreSQL on Render)
# DATABASE_URL=postgresql://username:password@hostname:port/database

# Server Configuration
PORT=5000
WEB_CONCURRENCY=4

# Logging
LOG_LEVEL=info

# Security (for production)
# SSL_DISABLE=False
# FORCE_HTTPS=True

# Optional: External Services
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=True
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# Optional: Redis (for caching/sessions)
# REDIS_URL=redis://localhost:6379/0

# Optional: File Upload
# UPLOAD_FOLDER=uploads
# MAX_CONTENT_LENGTH=16777216  # 16MB

# AI/ML Configuration
HF_TOKEN=your-huggingface-token-here
AI_MODEL_NAME=bigscience/bloomz-560m
AI_MAX_LENGTH=200
AI_TEMPERATURE=0.7
AI_CACHE_DIR=./model_cache

# Streamlit Configuration
STREAMLIT_PORT=8501
STREAMLIT_ENABLED=True
