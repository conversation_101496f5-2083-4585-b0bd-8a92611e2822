services:
  # Web Service
  - type: web
    name: flask-demo-app
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: gunicorn --config gunicorn.conf.py run:app
    envVars:
      - key: FLASK_ENV
        value: production
      - key: SECRET_KEY
        generateValue: true
      - key: DATABASE_URL
        fromDatabase:
          name: flask-demo-db
          property: connectionString
      - key: WEB_CONCURRENCY
        value: 4
      - key: LOG_LEVEL
        value: info
    healthCheckPath: /
    
  # Database Service
  - type: pserv
    name: flask-demo-db
    env: postgresql
    plan: free
    databaseName: flask_demo
    user: flask_user
    
# Optional: Background worker (if needed)
# - type: worker
#   name: flask-demo-worker
#   env: python
#   buildCommand: pip install -r requirements.txt
#   startCommand: python worker.py
#   envVars:
#     - key: FLASK_ENV
#       value: production
#     - key: DATABASE_URL
#       fromDatabase:
#         name: flask-demo-db
#         property: connectionString
