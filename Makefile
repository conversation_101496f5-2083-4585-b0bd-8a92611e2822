# Makefile for Flask Demo App

.PHONY: help install dev test clean docker-build docker-run deploy-render

# Default target
help:
	@echo "Flask Demo App - Available Commands:"
	@echo ""
	@echo "Development:"
	@echo "  install     - Install dependencies"
	@echo "  dev         - Run development server"
	@echo "  test        - Run tests"
	@echo "  coverage    - Run tests with coverage"
	@echo "  clean       - Clean up temporary files"
	@echo ""
	@echo "Docker:"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container"
	@echo ""
	@echo "Deployment:"
	@echo "  deploy-render - Show Render deployment instructions"
	@echo ""

# Install dependencies
install:
	pip install --upgrade pip
	pip install -r requirements.txt

# Run development server
dev:
	python run.py

# Run tests
test:
	pytest

# Run tests with coverage
coverage:
	pytest --cov=app --cov-report=term-missing --cov-report=html

# Clean up temporary files
clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	rm -rf .pytest_cache
	rm -rf htmlcov
	rm -rf .coverage
	rm -rf *.egg-info
	rm -rf build
	rm -rf dist

# Docker build
docker-build:
	docker build -t flask-demo-app .

# Docker run
docker-run:
	docker run -p 5000:5000 -e SECRET_KEY=dev-secret-key flask-demo-app

# Render deployment instructions
deploy-render:
	@echo "Render Deployment Instructions:"
	@echo "1. Push your code to GitHub"
	@echo "2. Connect your GitHub repo to Render"
	@echo "3. Render will automatically deploy using render.yaml"
	@echo "4. Set environment variables in Render dashboard"
	@echo ""
	@echo "Visit: https://render.com to get started"

# Setup development environment
setup:
	python -m venv venv
	@echo "Virtual environment created. Activate with:"
	@echo "  source venv/bin/activate  (Linux/Mac)"
	@echo "  venv\\Scripts\\activate     (Windows)"
	@echo "Then run: make install"

# Lint code (if you add linting tools)
lint:
	@echo "Add flake8 or black to requirements.txt for linting"
	# flake8 app tests
	# black --check app tests

# Format code (if you add formatting tools)
format:
	@echo "Add black to requirements.txt for formatting"
	# black app tests
