from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON><PERSON><PERSON>, TextAreaField, EmailField, SubmitField
from wtforms.validators import <PERSON>Required, Email, Length

class UserForm(FlaskForm):
    """Form for creating/editing users"""
    username = <PERSON><PERSON><PERSON>('Username', validators=[
        DataRequired(), 
        Length(min=3, max=20, message="Username must be between 3 and 20 characters")
    ])
    email = EmailField('Email', validators=[
        DataRequired(), 
        Email(message="Please enter a valid email address")
    ])
    submit = SubmitField('Submit')

class PostForm(FlaskForm):
    """Form for creating/editing posts"""
    title = StringField('Title', validators=[
        DataRequired(), 
        Length(min=5, max=100, message="Title must be between 5 and 100 characters")
    ])
    content = TextAreaField('Content', validators=[
        DataRequired(), 
        Length(min=10, message="Content must be at least 10 characters long")
    ])
    submit = SubmitField('Submit')
