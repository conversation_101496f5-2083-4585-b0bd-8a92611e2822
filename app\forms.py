from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, TextAreaField, EmailField, SubmitField, IntegerField, FloatField
from wtforms.validators import DataRequired, Email, Length, NumberRange

class UserForm(FlaskForm):
    """Form for creating/editing users"""
    username = StringField('Username', validators=[
        DataRequired(), 
        Length(min=3, max=20, message="Username must be between 3 and 20 characters")
    ])
    email = EmailField('Email', validators=[
        DataRequired(), 
        Email(message="Please enter a valid email address")
    ])
    submit = SubmitField('Submit')

class PostForm(FlaskForm):
    """Form for creating/editing posts"""
    title = StringField('Title', validators=[
        DataRequired(),
        Length(min=5, max=100, message="Title must be between 5 and 100 characters")
    ])
    content = TextAreaField('Content', validators=[
        DataRequired(),
        Length(min=10, message="Content must be at least 10 characters long")
    ])
    submit = SubmitField('Submit')

class AITextGenerationForm(FlaskForm):
    """Form for AI text generation"""
    prompt = TextAreaField('Prompt', validators=[
        DataRequired(),
        Length(min=1, max=1000, message="Prompt must be between 1 and 1000 characters")
    ], render_kw={
        "placeholder": "Enter your text prompt here...",
        "rows": 4
    })

    max_length = IntegerField('Max Length', validators=[
        NumberRange(min=50, max=500, message="Max length must be between 50 and 500")
    ], default=200)

    temperature = FloatField('Temperature', validators=[
        NumberRange(min=0.1, max=1.5, message="Temperature must be between 0.1 and 1.5")
    ], default=0.7)

    submit = SubmitField('Generate Text')
